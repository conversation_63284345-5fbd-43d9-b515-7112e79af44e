<template>
  <div class="py-12 px-4">
    <div class="max-w-7xl mx-auto">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
        <!-- Product Images -->
        <div class="space-y-4">
          <div class="relative overflow-hidden rounded-lg">
            <img
              :src="product.image"
              :alt="product.name"
              class="w-full h-[600px] object-cover"
            />
          </div>
          <div class="grid grid-cols-4 gap-4">
            <div
              v-for="(image, index) in product.images"
              :key="index"
              class="relative overflow-hidden rounded-lg cursor-pointer"
              @click="selectedImage = image"
            >
              <img
                :src="image"
                :alt="product.name"
                class="w-full h-24 object-cover"
              />
            </div>
          </div>
        </div>

        <!-- Product Info -->
        <div class="space-y-8">
          <div>
            <h1 class="text-3xl font-bold mb-2">{{ product.name }}</h1>
            <p class="text-2xl font-medium text-gray-900">{{ product.price }} TL</p>
          </div>

          <div class="space-y-4">
            <h2 class="text-lg font-medium">Renk</h2>
            <div class="flex space-x-4">
              <button
                v-for="color in product.colors"
                :key="color"
                class="w-8 h-8 rounded-full border-2 border-gray-200 focus:outline-none focus:ring-2 focus:ring-black"
                :style="{ backgroundColor: color }"
                @click="selectedColor = color"
              ></button>
            </div>
          </div>

          <div class="space-y-4">
            <h2 class="text-lg font-medium">Beden</h2>
            <div class="flex space-x-4">
              <button
                v-for="size in product.sizes"
                :key="size"
                class="px-4 py-2 border-2 border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                :class="{ 'border-black': selectedSize === size }"
                @click="selectedSize = size"
              >
                {{ size }}
              </button>
            </div>
          </div>

          <div class="space-y-4">
            <h2 class="text-lg font-medium">Açıklama</h2>
            <p class="text-gray-600">{{ product.description }}</p>
          </div>

          <button
            class="w-full bg-black text-white py-4 rounded-full text-lg font-medium hover:bg-gray-800 transition-colors duration-200"
            @click="addToCart"
          >
            Sepete Ekle
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const selectedColor = ref(null)
const selectedSize = ref(null)
const selectedImage = ref(null)

const product = ref({
  id: 1,
  name: 'Slim Fit Gömlek',
  price: '599',
  description: 'Premium kalite pamuktan üretilen slim fit kesim gömlek. Günlük kullanım için ideal, şık ve rahat tasarım.',
  image: 'https://images.unsplash.com/photo-1603252109303-2751441dd157?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
  images: [
    'https://images.unsplash.com/photo-1603252109303-2751441dd157?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
    'https://images.unsplash.com/photo-1603252109303-2751441dd157?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
    'https://images.unsplash.com/photo-1603252109303-2751441dd157?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
    'https://images.unsplash.com/photo-1603252109303-2751441dd157?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80'
  ],
  colors: ['#000000', '#FFFFFF', '#808080', '#0000FF'],
  sizes: ['S', 'M', 'L', 'XL']
})

onMounted(() => {
  // TODO: Fetch product data based on route.params.id
  selectedImage.value = product.value.image
})

const addToCart = () => {
  if (!selectedColor.value || !selectedSize.value) {
    alert('Lütfen renk ve beden seçiniz.')
    return
  }
  // TODO: Implement cart functionality
  console.log('Added to cart:', {
    ...product.value,
    selectedColor: selectedColor.value,
    selectedSize: selectedSize.value
  })
}
</script> 