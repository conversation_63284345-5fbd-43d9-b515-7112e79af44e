<template>
  <div class="py-12 px-4">
    <div class="max-w-7xl mx-auto">
      <!-- Filters -->
      <div class="mb-8">
        <div class="flex flex-wrap gap-4">
          <button
            v-for="category in categories"
            :key="category.id"
            @click="selectedCategory = category.id"
            :class="[
              'px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200',
              selectedCategory === category.id
                ? 'bg-black text-white'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            ]"
          >
            {{ category.name }}
          </button>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        <div
          v-for="product in filteredProducts"
          :key="product.id"
          class="group"
          @click="navigateToProduct(product.id)"
        >
          <div class="relative overflow-hidden rounded-lg mb-4 cursor-pointer">
            <img
              :src="product.image"
              :alt="product.name"
              class="w-full h-96 object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
            <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button
                class="w-full bg-white text-black py-2 rounded-full text-sm font-medium hover:bg-gray-100 transition-colors duration-200"
                @click.stop="addToCart(product)"
              >
                Sepete Ekle
              </button>
            </div>
          </div>
          <h3 class="text-lg font-medium">{{ product.name }}</h3>
          <p class="text-gray-600">{{ product.price }} TL</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const selectedCategory = ref(null)

const categories = ref([
  { id: 1, name: 'Tümü' },
  { id: 2, name: 'Gömlekler' },
  { id: 3, name: 'Pantolonlar' },
  { id: 4, name: 'Ceketler' },
  { id: 5, name: 'Kazaklar' }
])

const products = ref([
  {
    id: 1,
    name: 'Slim Fit Gömlek',
    price: '599',
    category: 2,
    image: 'https://images.unsplash.com/photo-1603252109303-2751441dd157?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80'
  },
  {
    id: 2,
    name: 'Klasik Pantolon',
    price: '799',
    category: 3,
    image: 'https://images.unsplash.com/photo-1624378439575-d8705ad7ae80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1997&q=80'
  },
  {
    id: 3,
    name: 'Blazer Ceket',
    price: '1299',
    category: 4,
    image: 'https://images.unsplash.com/photo-1593032465175-481ac7f401a0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80'
  },
  {
    id: 4,
    name: 'Triko Kazak',
    price: '899',
    category: 5,
    image: 'https://images.unsplash.com/photo-1638890816165-77a3e723d527?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80'
  }
])

const filteredProducts = computed(() => {
  if (!selectedCategory.value || selectedCategory.value === 1) {
    return products.value
  }
  return products.value.filter(product => product.category === selectedCategory.value)
})

const navigateToProduct = (productId) => {
  router.push(`/product/${productId}`)
}

const addToCart = (product) => {
  // TODO: Implement cart functionality
  console.log('Added to cart:', product)
}
</script> 