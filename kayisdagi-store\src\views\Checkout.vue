<template>
  <div class="py-12 px-4">
    <div class="max-w-7xl mx-auto">
      <h1 class="text-3xl font-bold mb-8">Ödeme</h1>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Checkout Form -->
        <div class="lg:col-span-2 space-y-8">
          <!-- Shipping Information -->
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-bold mb-4">Teslimat Bilgileri</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Ad</label>
                <input
                  type="text"
                  v-model="shippingInfo.firstName"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Soyad</label>
                <input
                  type="text"
                  v-model="shippingInfo.lastName"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">Adres</label>
                <input
                  type="text"
                  v-model="shippingInfo.address"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Şehir</label>
                <input
                  type="text"
                  v-model="shippingInfo.city"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Posta Kodu</label>
                <input
                  type="text"
                  v-model="shippingInfo.postalCode"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
            </div>
          </div>

          <!-- Payment Information -->
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-bold mb-4">Ödeme Bilgileri</h2>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Kart Üzerindeki İsim</label>
                <input
                  type="text"
                  v-model="paymentInfo.cardName"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Kart Numarası</label>
                <input
                  type="text"
                  v-model="paymentInfo.cardNumber"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="1234 5678 9012 3456"
                />
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Son Kullanma Tarihi</label>
                  <input
                    type="text"
                    v-model="paymentInfo.expiryDate"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="MM/YY"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">CVV</label>
                  <input
                    type="text"
                    v-model="paymentInfo.cvv"
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="123"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-bold mb-4">Sipariş Özeti</h2>
            <div class="space-y-4">
              <div v-for="item in cartItems" :key="item.id" class="flex items-center space-x-4">
                <img
                  :src="item.image"
                  :alt="item.name"
                  class="w-16 h-16 object-cover rounded-lg"
                />
                <div class="flex-1">
                  <h3 class="text-sm font-medium">{{ item.name }}</h3>
                  <p class="text-sm text-gray-600">Adet: {{ item.quantity }}</p>
                </div>
                <p class="text-sm font-medium">{{ item.price * item.quantity }} TL</p>
              </div>
              <div class="border-t pt-4 space-y-2">
                <div class="flex justify-between">
                  <span class="text-gray-600">Ara Toplam</span>
                  <span>{{ subtotal }} TL</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Kargo</span>
                  <span>Ücretsiz</span>
                </div>
                <div class="flex justify-between font-bold">
                  <span>Toplam</span>
                  <span>{{ subtotal }} TL</span>
                </div>
              </div>
              <button
                class="w-full bg-black text-white py-4 rounded-full text-lg font-medium hover:bg-gray-800 transition-colors duration-200"
                @click="placeOrder"
              >
                Siparişi Tamamla
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const shippingInfo = ref({
  firstName: '',
  lastName: '',
  address: '',
  city: '',
  postalCode: ''
})

const paymentInfo = ref({
  cardName: '',
  cardNumber: '',
  expiryDate: '',
  cvv: ''
})

const cartItems = ref([
  {
    id: 1,
    name: 'Slim Fit Gömlek',
    price: 599,
    image: 'https://images.unsplash.com/photo-1603252109303-2751441dd157?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
    quantity: 1
  },
  {
    id: 2,
    name: 'Klasik Pantolon',
    price: 799,
    image: 'https://images.unsplash.com/photo-1624378439575-d8705ad7ae80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1997&q=80',
    quantity: 1
  }
])

const subtotal = computed(() => {
  return cartItems.value.reduce((total, item) => {
    return total + (item.price * item.quantity)
  }, 0)
})

const placeOrder = () => {
  // TODO: Implement order placement
  console.log('Order placed:', {
    shippingInfo: shippingInfo.value,
    paymentInfo: paymentInfo.value,
    items: cartItems.value,
    total: subtotal.value
  })
  // Redirect to success page or show success message
}
</script> 