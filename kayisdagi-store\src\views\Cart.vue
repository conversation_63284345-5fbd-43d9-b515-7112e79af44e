<template>
  <div class="py-12 px-4">
    <div class="max-w-7xl mx-auto">
      <h1 class="text-3xl font-bold mb-8">Alışveriş Sepeti</h1>

      <div v-if="cartItems.length === 0" class="text-center py-12">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-16 w-16 mx-auto text-gray-400 mb-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
          />
        </svg>
        <p class="text-xl text-gray-600 mb-4">Sepetiniz boş</p>
        <router-link
          to="/products"
          class="inline-block bg-black text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-gray-800 transition-colors duration-200"
        >
          Alışverişe Başla
        </router-link>
      </div>

      <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Cart Items -->
        <div class="lg:col-span-2 space-y-4">
          <div
            v-for="item in cartItems"
            :key="item.id"
            class="flex items-center space-x-4 p-4 bg-white rounded-lg shadow-sm"
          >
            <img
              :src="item.image"
              :alt="item.name"
              class="w-24 h-24 object-cover rounded-lg"
            />
            <div class="flex-1">
              <h3 class="text-lg font-medium">{{ item.name }}</h3>
              <p class="text-gray-600">Beden: {{ item.selectedSize }}</p>
              <p class="text-gray-600">Renk: {{ item.selectedColor }}</p>
              <p class="text-lg font-medium mt-2">{{ item.price }} TL</p>
            </div>
            <div class="flex items-center space-x-2">
              <button
                class="w-8 h-8 flex items-center justify-center border border-gray-200 rounded-full hover:bg-gray-100"
                @click="updateQuantity(item, -1)"
              >
                -
              </button>
              <span class="w-8 text-center">{{ item.quantity }}</span>
              <button
                class="w-8 h-8 flex items-center justify-center border border-gray-200 rounded-full hover:bg-gray-100"
                @click="updateQuantity(item, 1)"
              >
                +
              </button>
            </div>
            <button
              class="text-red-500 hover:text-red-700"
              @click="removeItem(item)"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-bold mb-4">Sipariş Özeti</h2>
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-gray-600">Ara Toplam</span>
                <span>{{ subtotal }} TL</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Kargo</span>
                <span>Ücretsiz</span>
              </div>
              <div class="border-t pt-4">
                <div class="flex justify-between font-bold">
                  <span>Toplam</span>
                  <span>{{ subtotal }} TL</span>
                </div>
              </div>
              <button
                class="w-full bg-black text-white py-4 rounded-full text-lg font-medium hover:bg-gray-800 transition-colors duration-200"
                @click="checkout"
              >
                Ödemeye Geç
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const cartItems = ref([
  {
    id: 1,
    name: 'Slim Fit Gömlek',
    price: '599',
    image: 'https://images.unsplash.com/photo-1603252109303-2751441dd157?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
    selectedSize: 'M',
    selectedColor: '#000000',
    quantity: 1
  },
  {
    id: 2,
    name: 'Klasik Pantolon',
    price: '799',
    image: 'https://images.unsplash.com/photo-1624378439575-d8705ad7ae80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1997&q=80',
    selectedSize: 'L',
    selectedColor: '#808080',
    quantity: 1
  }
])

const subtotal = computed(() => {
  return cartItems.value.reduce((total, item) => {
    return total + (parseInt(item.price) * item.quantity)
  }, 0)
})

const updateQuantity = (item, change) => {
  const newQuantity = item.quantity + change
  if (newQuantity > 0) {
    item.quantity = newQuantity
  }
}

const removeItem = (item) => {
  const index = cartItems.value.indexOf(item)
  if (index > -1) {
    cartItems.value.splice(index, 1)
  }
}

const checkout = () => {
  router.push('/checkout')
}
</script> 