{"name": "kayisdagi-store", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^13.3.0", "autoprefixer": "^10.4.21", "gsap": "^3.13.0", "pinia": "^3.0.3", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.3.5"}}